load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "nacos",
    srcs = [
        "client.go",
        "plugin.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/common/rpc/config/nacos",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/framework",
        "//backend/common/rpc/framework/config",
        "//backend/common/rpc/framework/log",
        "//backend/common/rpc/framework/plugin",
        "@com_github_nacos_group_nacos_sdk_go_v2//clients",
        "@com_github_nacos_group_nacos_sdk_go_v2//clients/config_client",
        "@com_github_nacos_group_nacos_sdk_go_v2//common/constant",
        "@com_github_nacos_group_nacos_sdk_go_v2//vo",
        "@org_uber_go_zap//:zap",
    ],
)
