package service

import (
	"context"
	"fmt"
	"strconv"
	"time"

	eventbuslib "github.com/MoeGolibrary/go-lib/event-bus"
	"github.com/MoeGolibrary/go-lib/merror"
	"github.com/MoeGolibrary/go-lib/zlog"
	errorsModelsV1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/errors/v1"
	eventbuspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1"
	fulfillmentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/fulfillment/v1"
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	fulfillmentsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/fulfillment/v1"
	offeringsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"
	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1"
	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/consts"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/converter"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/converter/impl"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/customer"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/fulfillment"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/fulfillment/filter"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/model"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/offering"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/order"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/organization"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/query"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/service/dto"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/util"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
)

type CancelFulfillmentOpt struct {
	AutoRefundOrder bool
}

var FulfillmentConverter = &impl.FulfillmentConverter{}
var GroupClassConverter = &impl.GroupClassConverter{}
var StaffTimeSlotConverter = &impl.StaffTimeSlotConverter{}

type FulfillmentService interface {
	WithQuery(*query.Query) FulfillmentService

	EnrollPet(context.Context, dto.PetEnrollmentDTO) (int64, error)
	RemovePet(context.Context, *fulfillmentsvcpb.RemovePetRequest) (int64, error)
	ListFulfillments(context.Context, dto.ListFulfillmentsDTO) ([]*model.Fulfillment, *utilsV2.PaginationResponse, error)
	CreateFulfillment(context.Context, dto.CreateFulfillmentWithDetailsDTO) (int64, int64, error)
	GetFulfillment(context.Context, int64) (*model.Fulfillment, error)
	UpdateFulfillment(context.Context, dto.UpdateFulfillmentDTO) error
	SyncFulfillmentStatus(context.Context) error
	CancelFulfillment(context.Context, *query.Query, int64, CancelFulfillmentOpt) error
}

type fulfillmentService struct {
	query *query.Query

	groupClassService GroupClassService

	fulfillmentRepository      fulfillment.Repository
	groupClassDetailRepository fulfillment.GroupClassDetailRepository
	staffTimeSlotRepository    fulfillment.StaffTimeSlotRepository
	attendanceRepository       fulfillment.GroupClassAttendanceRepository

	petRepository        customer.PetRepository
	customerRepository   customer.Repository
	orderRepository      order.Repository
	serviceRepository    offering.ServiceRepository
	taxRepository        organization.TaxRepository
	groupClassRepository offering.GroupClassRepository

	producer eventbuslib.Producer[proto.Message]
}

func (s fulfillmentService) SyncFulfillmentStatus(ctx context.Context) error {
	pendingFulfillments, _, err := s.ListFulfillments(ctx, dto.ListFulfillmentsDTO{
		FulfillmentFilter: &dto.FulfillmentFilter{
			Statuses:     []fulfillmentpb.Status{fulfillmentpb.Status_PENDING_PAYMENT},
			CreatedAtMin: lo.ToPtr(time.Now().Add(-24 * time.Hour)),
		},
	})
	if err != nil {
		return errors.WithStack(err)
	}
	orderIDs := lo.Map(pendingFulfillments, func(f *model.Fulfillment, _ int) int64 {
		return f.OrderID
	})
	orders, err := s.orderRepository.BatchGetOrder(ctx, orderIDs)
	if err != nil {
		return errors.WithStack(err)
	}
	completedOrders := lo.Filter(orders, func(order *orderpb.OrderDetailModel, _ int) bool {
		return order.GetOrder().GetStatus() == int32(orderpb.OrderStatus_COMPLETED)
	})
	if len(completedOrders) == 0 {
		return nil
	}
	completedOrderIDs := lo.Map(completedOrders, func(order *orderpb.OrderDetailModel, _ int) int64 {
		return order.GetOrder().GetId()
	})
	paidFulfillments := lo.Filter(pendingFulfillments, func(f *model.Fulfillment, _ int) bool {
		return lo.Contains(completedOrderIDs, f.OrderID)
	})

	for _, paidFulfillment := range paidFulfillments {
		if err = s.fulfillmentRepository.UpdateSelective(ctx, dto.UpdateFulfillmentDTO{
			FulfillmentID: paidFulfillment.ID,
			Status:        lo.ToPtr(fulfillmentpb.Status_UNCONFIRMED),
		}); err != nil {
			return err
		}
		zlog.Info(ctx, "Update paid fulfillment status to UNCONFIRMED",
			zap.Int64("fulfillment_id", paidFulfillment.ID))
	}
	return nil
}

func (s fulfillmentService) UpdateFulfillment(ctx context.Context, updateDTO dto.UpdateFulfillmentDTO) error {
	if err := s.query.Transaction(func(tx *query.Query) error {
		// 1. update fulfillment
		if err := s.fulfillmentRepository.
			WithQuery(tx).UpdateSelective(ctx, updateDTO); err != nil {
			return err
		}
		// 2. update group class detail
		if err := s.groupClassDetailRepository.
			WithQuery(tx).BatchUpdateSelective(ctx, updateDTO.GroupClasses); err != nil {
			return err
		}
		// 3. update staff time slot
		if err := s.staffTimeSlotRepository.
			WithQuery(tx).BatchUpdateSelective(ctx, updateDTO.StaffTimeSlots); err != nil {
			return err
		}

		return nil
	}); err != nil {
		return err
	}
	return nil
}

func (s fulfillmentService) GetFulfillment(ctx context.Context, ID int64) (*model.Fulfillment, error) {
	return s.fulfillmentRepository.GetByID(ctx, ID)
}

func (s fulfillmentService) CancelFulfillment(ctx context.Context, tx *query.Query, fulfillmentID int64, cancelOpt CancelFulfillmentOpt) error {
	update := dto.UpdateFulfillmentDTO{
		FulfillmentID: fulfillmentID,
		Status:        lo.ToPtr(fulfillmentpb.Status_CANCELED),
	}

	if err := s.fulfillmentRepository.WithQuery(tx).UpdateSelective(ctx, update); err != nil {
		return err
	}

	go s.sendFulfillmentCanceledEvent(ctx, fulfillmentID, cancelOpt)

	return nil
}

func (s fulfillmentService) listInstances(
	ctx context.Context, groupClassDTOs []*dto.GroupClassDetailsDTO) ([]*offeringpb.GroupClassInstance, error) {
	instanceIDs := lo.Map(groupClassDTOs, func(item *dto.GroupClassDetailsDTO, _ int) int64 {
		return item.GroupClassInstanceID
	})
	instances, err := s.groupClassRepository.ListInstances(ctx, lo.Uniq(instanceIDs))
	if err != nil || len(instances) == 0 {
		zlog.Error(ctx, "failed to get instances", zap.Error(err))
		return nil, status.Errorf(codes.Internal, "failed to get instances: %v", err)
	}
	return instances, nil
}

func (s fulfillmentService) listServices(
	ctx context.Context, companyId int64, groupClassDTOs []*dto.GroupClassDetailsDTO, instances []*offeringpb.GroupClassInstance) ([]*offeringsvcpb.BatchGetCustomizedServiceResponse_ServiceWithCustomizedInfo, error) {
	isToInstance := lo.KeyBy(instances, func(item *offeringpb.GroupClassInstance) int64 {
		return item.Id
	})
	conditions := lo.Map(groupClassDTOs, func(item *dto.GroupClassDetailsDTO, _ int) *offeringsvcpb.CustomizedServiceQueryCondition {
		instance := isToInstance[item.GroupClassInstanceID]
		return &offeringsvcpb.CustomizedServiceQueryCondition{
			ServiceId:  instance.GroupClassId,
			BusinessId: &instance.BusinessId,
			PetId:      &item.PetID,
			StaffId:    &instance.StaffId,
		}
	})
	customizedServices, err := s.serviceRepository.BatchGetCustomizedServices(ctx, companyId, conditions)
	if err != nil || len(customizedServices) == 0 {
		zlog.Error(ctx, "failed to get services", zap.Error(err))
		return nil, status.Errorf(codes.Internal, "failed to get services: %v", err)
	}
	return customizedServices, nil
}

func (s fulfillmentService) listTaxes(
	ctx context.Context, services []*offeringsvcpb.BatchGetCustomizedServiceResponse_ServiceWithCustomizedInfo) ([]*organizationpb.TaxRuleModel, error) {
	taxIds := lo.Map(services, func(item *offeringsvcpb.BatchGetCustomizedServiceResponse_ServiceWithCustomizedInfo, _ int) int64 {
		return item.CustomizedService.TaxId
	})
	taxes, err := s.taxRepository.ListTaxRules(ctx, lo.Uniq(taxIds))
	if err != nil || len(taxes) == 0 {
		zlog.Error(ctx, "failed to get taxes", zap.Error(err))
		return nil, status.Errorf(codes.Internal, "failed to get taxes: %v", err)
	}
	return taxes, nil
}

func (s fulfillmentService) CreateFulfillment(ctx context.Context, createDTO dto.CreateFulfillmentWithDetailsDTO) (int64, int64, error) {
	customerInfo, err := s.customerRepository.GetCustomerInfo(ctx, createDTO.CustomerID)
	if err != nil {
		return 0, 0, err
	}
	instances, err := s.listInstances(ctx, createDTO.GroupClasses)
	if err != nil {
		return 0, 0, err
	}
	services, err := s.listServices(ctx, createDTO.CompanyID, createDTO.GroupClasses, instances)
	if err != nil {
		return 0, 0, err
	}
	taxes, err := s.listTaxes(ctx, services)
	if err != nil {
		return 0, 0, err
	}

	var fulfillmentID int64
	var orderID int64
	err = s.query.Transaction(func(tx *query.Query) error {
		// 1. create fulfillment
		fulfillmentModel := FulfillmentConverter.DTOToModel(&createDTO)
		fulfillmentModel.ServiceTypeInclude = converter.ExtractServiceTypeInclude(&createDTO)
		err = s.fulfillmentRepository.WithQuery(tx).Create(ctx, fulfillmentModel)
		if err != nil {
			return err
		}

		staffTimeSlots := make([]*model.StaffTimeSlot, 0)
		// TODO 包含各种类型的 service/add-on
		// 2. create group class detail and staff time slots
		idToInstance := lo.KeyBy(instances, func(instance *offeringpb.GroupClassInstance) int64 {
			return instance.Id
		})
		groupClassDetails := make([]*model.GroupClassDetail, 0)
		for _, groupClass := range createDTO.GroupClasses {
			classDTO := dto.CreateGroupClassDTO{
				CompanyID:          createDTO.CompanyID,
				BusinessID:         createDTO.BusinessID,
				FulfillmentID:      fulfillmentModel.ID,
				PetID:              groupClass.PetID,
				CustomerID:         createDTO.CustomerID,
				GroupClassInstance: idToInstance[groupClass.GroupClassInstanceID],
			}
			groupClassDetail, groupClassStaffTimeSlots, err := s.groupClassService.WithQuery(tx).BatchCreate(ctx, classDTO)
			if err != nil {
				return err
			}
			groupClassDetails = append(groupClassDetails, groupClassDetail)
			staffTimeSlots = append(staffTimeSlots, groupClassStaffTimeSlots...)
		}

		// 3. create order
		orderModel := converter.FulfillmentToOrder(fulfillmentModel, customerInfo, services, createDTO.CreatedBy)
		lineItemModels := converter.GroupClassToOrderLineItems(
			fulfillmentModel, groupClassDetails, services, taxes, instances, createDTO.CreatedBy)
		createOrderRequest := &ordersvcpb.CreateOrderRequest{
			Order:     orderModel,
			LineItems: lineItemModels,
		}
		orderDetail, err := s.orderRepository.CreateOrder(ctx, createOrderRequest)
		if err != nil {
			return err
		}

		// 4. update fulfillment range and order id
		startTime, endTime := util.GetTimeRange(staffTimeSlots)
		if err := s.fulfillmentRepository.
			WithQuery(tx).UpdateSelective(ctx, dto.UpdateFulfillmentDTO{
			FulfillmentID: fulfillmentModel.ID,
			StartDatetime: &startTime,
			EndDatetime:   &endTime,
			OrderID:       orderDetail.Order.Id,
		}); err != nil {
			return err
		}

		// 5. update order line item id
		itemKeyToOrderLineItem := converter.GroupByItemKey(orderDetail.LineItems)
		updateStaffTimeSlots := make([]dto.UpdateStaffTimeSlotDTO, 0)
		detailIdToServiceId := lo.KeyBy(groupClassDetails, func(detail *model.GroupClassDetail) string {
			return s.buildServiceDetailKey(offeringpb.ServiceItemType_GROUP_CLASS, detail.ID)
		})
		for _, staffTimeSlot := range staffTimeSlots {
			// CareType + DetailID 取 petId + serviceId
			groupClassDetail := detailIdToServiceId[s.buildServiceDetailKey(staffTimeSlot.CareType, staffTimeSlot.DetailID)]
			customizedService := converter.FilterCustomizedService(services, &offeringsvcpb.CustomizedServiceQueryCondition{
				ServiceId:  groupClassDetail.GroupClassID,
				BusinessId: &staffTimeSlot.BusinessID,
				PetId:      &groupClassDetail.PetID,
				StaffId:    &staffTimeSlot.StaffID,
			})
			orderLineItem := itemKeyToOrderLineItem[converter.BuildOrderItemKey(
				groupClassDetail.PetID, customizedService.Id, string(consts.OrderItemType_Service), customizedService.Price)]
			updateStaffTimeSlots = append(updateStaffTimeSlots, dto.UpdateStaffTimeSlotDTO{
				StaffTimeSlotID: staffTimeSlot.ID,
				OrderLineItemID: orderLineItem.Id,
			})
		}
		if err := s.staffTimeSlotRepository.WithQuery(tx).BatchUpdateSelective(ctx, updateStaffTimeSlots); err != nil {
			return err
		}
		fulfillmentID = fulfillmentModel.ID
		orderID = orderDetail.Order.GetId()
		return nil
	})
	if err != nil {
		return 0, 0, err
	}
	return fulfillmentID, orderID, nil
}

func (s fulfillmentService) buildServiceDetailKey(serviceItemType offeringpb.ServiceItemType, detailID int64) string {
	return fmt.Sprintf("%d-%d", serviceItemType.Number(), detailID)
}

func (s fulfillmentService) ListFulfillments(ctx context.Context, dto dto.ListFulfillmentsDTO) ([]*model.Fulfillment, *utilsV2.PaginationResponse, error) {
	fulfillmentFilter := &filter.ListFulfillmentFilter{
		CompanyID:    &dto.CompanyID,
		BusinessIDs:  dto.BusinessIDs,
		IDs:          dto.FulfillmentFilter.IDs,
		Statuses:     dto.FulfillmentFilter.Statuses,
		CustomerIDs:  dto.FulfillmentFilter.CustomerIds,
		CreatedAtMin: dto.FulfillmentFilter.CreatedAtMin,
	}
	if dto.GroupClassFilter != nil {
		filter := filter.ListGroupClassDetailFilter{
			PetIDs:        dto.GroupClassFilter.PetIDs,
			GroupClassIDs: dto.GroupClassFilter.GroupClassIDs,
		}
		groupClassDetails, err := s.groupClassDetailRepository.ListByFilter(ctx, filter)
		if err != nil {
			return nil, nil, err
		}
		fulfillmentFilter.IDs = lo.Map(groupClassDetails, func(detail *model.GroupClassDetail, _ int) int64 {
			return detail.FulfillmentID
		})
	}

	fulfillments, err := s.fulfillmentRepository.List(ctx, fulfillmentFilter, dto.Pagination)
	if err != nil {
		return nil, nil, err
	}

	return fulfillments, &utilsV2.PaginationResponse{}, nil
}

// getFulfillment 获取已存在的 fulfillment
func (s fulfillmentService) getFulfillment(ctx context.Context, petID, instanceID int64) (*model.Fulfillment, error) {
	class, err := s.groupClassService.GetByPetAndInstance(ctx, petID, instanceID)
	if err != nil || class == nil {
		return nil, err
	}
	ffm, err := s.fulfillmentRepository.GetByID(ctx, class.FulfillmentID)
	if err != nil {
		return nil, err
	}
	return ffm, nil
}

// validateCapacity 检查实例容量是否已满
func (s fulfillmentService) validateCapacity(ctx context.Context, instanceID int64) error {
	//instance, err := s.groupClassRepository.GetInstance(ctx, instanceID)
	//if err != nil {
	//	return err
	//}
	//count, err := s.groupClassDetailRepository.CountByInstanceID(ctx, instanceID)
	//if err != nil {
	//	return err
	//}
	//if count >= int64(instance.Capacity) {
	//	return fmt.Errorf("capacity exceeded for group class instance: %d", instanceID)
	//}
	return nil
}

func (s fulfillmentService) EnrollPet(ctx context.Context, enrollmentDTO dto.PetEnrollmentDTO) (int64, error) {
	// 1. get existing fulfillment
	ffm, err := s.getFulfillment(ctx, enrollmentDTO.PetID, enrollmentDTO.GroupClassInstanceID)
	if err != nil {
		return 0, err
	}

	if ffm != nil && ffm.OrderID > 0 {
		return ffm.OrderID, nil
	}

	// 2. check capacity
	if err := s.validateCapacity(ctx, enrollmentDTO.GroupClassInstanceID); err != nil {
		zlog.Error(ctx, "EnrollPet failed, capacity check failed",
			zap.Int64("group_class_instance_id", enrollmentDTO.GroupClassInstanceID), zap.Error(err))
		return 0, err
	}

	// 3. create fulfillment
	petInfo, err := s.petRepository.GetPetInfo(ctx, enrollmentDTO.PetID)
	if err != nil {
		return 0, err
	}
	_, orderID, err := s.CreateFulfillment(ctx, dto.CreateFulfillmentWithDetailsDTO{
		CompanyID:  enrollmentDTO.CompanyID,
		BusinessID: enrollmentDTO.BusinessID,
		CustomerID: petInfo.CustomerId,
		Status:     fulfillmentpb.Status_PENDING_PAYMENT,
		Source:     enrollmentDTO.Source,
		CreatedBy:  enrollmentDTO.StaffID,
		GroupClasses: []*dto.GroupClassDetailsDTO{
			{
				PetID:                enrollmentDTO.PetID,
				GroupClassInstanceID: enrollmentDTO.GroupClassInstanceID,
			},
		},
	})
	if err != nil {
		return 0, err
	}

	return orderID, nil
}

func (s fulfillmentService) RemovePet(ctx context.Context, req *fulfillmentsvcpb.RemovePetRequest) (int64, error) {
	ffm, err := s.getFulfillment(ctx, req.GetPetId(), req.GetInstanceId())
	if err != nil {
		return 0, err
	}
	if ffm == nil || (req.CompanyId != nil && ffm.CompanyID != req.GetCompanyId()) {
		return 0, merror.NewBizError(errorsModelsV1.Code_CODE_PARAMS_ERROR, fmt.Sprintf("fulfillment not found for pet_id=%d, instance_id=%d", req.GetPetId(), req.GetInstanceId()))
	}

	groupClassDetail, err := s.groupClassService.GetByPetAndInstance(ctx, req.GetPetId(), req.GetInstanceId())
	if err != nil {
		return 0, err
	}
	if groupClassDetail == nil {
		return 0, merror.NewBizError(errorsModelsV1.Code_CODE_PARAMS_ERROR, fmt.Sprintf("group class detail not found for pet_id=%d, instance_id=%d", req.GetPetId(), req.GetInstanceId()))
	}

	err = s.query.Transaction(func(tx *query.Query) error {
		// 1) Delete group class detail
		if err := s.groupClassService.WithQuery(tx).DeleteByPetAndInstance(ctx, req.GetPetId(), req.GetInstanceId()); err != nil {
			return err
		}

		// 2) Delete related staff time slots
		if err := s.staffTimeSlotRepository.WithQuery(tx).DeleteByFulfillmentID(ctx, ffm.ID); err != nil {
			return err
		}

		// 3) Delete related attendance records
		if err := s.attendanceRepository.WithQuery(tx).DeleteByFulfillmentID(ctx, ffm.ID); err != nil {
			return err
		}

		// 4) Cancel the fulfillment
		if err := s.CancelFulfillment(ctx, tx, ffm.ID, CancelFulfillmentOpt{AutoRefundOrder: req.GetAutoRefundOrder()}); err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return 0, err
	}

	return ffm.OrderID, nil
}

func (s fulfillmentService) sendFulfillmentCanceledEvent(ctx context.Context, fulfillmentID int64, opt CancelFulfillmentOpt) {
	eventData := &eventbuspb.EventData{
		Event: &eventbuspb.EventData_FulfillmentCanceledEvent{
			FulfillmentCanceledEvent: &eventbuspb.FulfillmentCanceledEvent{
				Id:              fulfillmentID,
				AutoRefundOrder: opt.AutoRefundOrder,
			},
		},
	}

	event := &eventbuslib.Event[proto.Message]{
		ID:        strconv.FormatInt(fulfillmentID, 10) + "-" + strconv.FormatInt(time.Now().UnixNano(), 10),
		Detail:    eventData,
		EventType: eventbuspb.EventType_FULFILLMENT_CANCELED,
	}

	if err := s.producer.SendMessage(context.Background(), event); err != nil {
		zlog.Error(ctx, "failed to send FulfillmentCanceledEvent",
			zap.Int64("fulfillment_id", fulfillmentID),
			zap.Bool("auto_refund_order", opt.AutoRefundOrder),
			zap.Error(err))
	} else {
		zlog.Info(ctx, "FulfillmentCanceledEvent sent successfully",
			zap.Int64("fulfillment_id", fulfillmentID),
			zap.Bool("auto_refund_order", opt.AutoRefundOrder))
	}
}

// with query
func (s fulfillmentService) WithQuery(q *query.Query) FulfillmentService {
	if q != nil {
		return NewFulfillmentService(q, s.groupClassService, s.fulfillmentRepository, s.groupClassDetailRepository, s.staffTimeSlotRepository, s.attendanceRepository, s.petRepository, s.customerRepository, s.orderRepository, s.serviceRepository, s.taxRepository, s.groupClassRepository, s.producer)
	}
	return s
}

func NewFulfillmentService(
	q *query.Query,
	groupClassService GroupClassService,
	fulfillmentRepository fulfillment.Repository,
	groupClassRepository fulfillment.GroupClassDetailRepository,
	staffTimeSlotRepository fulfillment.StaffTimeSlotRepository,
	attendanceRepository fulfillment.GroupClassAttendanceRepository,
	petRepository customer.PetRepository,
	customerRepository customer.Repository,
	orderRepository order.Repository,
	serviceRepository offering.ServiceRepository,
	taxRepository organization.TaxRepository,
	trainBatchRepository offering.GroupClassRepository,
	producer eventbuslib.Producer[proto.Message],
) FulfillmentService {
	return &fulfillmentService{
		query:                      q,
		groupClassService:          groupClassService,
		fulfillmentRepository:      fulfillmentRepository,
		groupClassDetailRepository: groupClassRepository,
		staffTimeSlotRepository:    staffTimeSlotRepository,
		attendanceRepository:       attendanceRepository,
		petRepository:              petRepository,
		customerRepository:         customerRepository,
		orderRepository:            orderRepository,
		serviceRepository:          serviceRepository,
		taxRepository:              taxRepository,
		groupClassRepository:       trainBatchRepository,
		producer:                   producer,
	}
}
